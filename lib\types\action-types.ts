export interface VerifyCaptchaRequest {
    captcha: string;
}

export interface VerifyCaptchaResponse {
    success: boolean;
    message?: string;
}

export enum StatusTransaction {
    pending = "pending",
    success = "success",
    cancel = "cancel",
    reject = "reject",
}

export type HistoryTransaction = {
    trace_number: string;
    type: VehicleType;
    detail: boolean;
    detail_phone: string | null;
    detail_national_id: string | null;
    "plaque": Plaque,
    status_transaction: StatusTransaction;
    created_at: string;
};

export type PaymentDetail = {
    unique_id: string;
    amount: string;
    bill_id: string;
    city: string;
    date_time: string;
    delivery: string;
    has_image: boolean;
    location: string;
    officer_identification_code: string;
    payment_id: string;
    serial_number: string;
    type: string;
    type_code: string;
    payment_url: string;
};

export type CarPaymentDetails = {
    type: VehicleType;
    with_detail_type: boolean;
    amount: string;
    bill_id: string;
    payment_id: string;
    plaque_details: Details
    complaint_status: string;
    complaint_code: string;
    plaque: Plaque;
    payment_url: string;
    trace_number: string;
    date_inquiry: string;
    details: PaymentDetail[];
    message?: string
};

export class ApiService<T = any> {
    serverCookies?: string
    headers?: Record<string, string> = {}
    payload?: T
    baseUrl?: string
}

export interface ResponseResult<T> {
    data: T
    success: boolean
    status: number,
}

export type AuthResponse = {
    message: string
    token: string
    expire_time: number
}

export type VerificationArgs = {
    phone: string,
    code: string,
    token: string
}

interface Details {
    phone: string;
    national_id: string;
}

interface Plaque {
    left: string;
    mid?: string;
    right: string;
    alphabet?: string;
}

export interface PaymentArg {
    amount: number;
    callback_url: string
    type?: VehicleType,
    details?: Details;
    plaque?: Plaque;
}

type VehicleType = 'car' | 'motor' | string

export interface PaymentResponse {
    message: string,
    payment_url: string,
    callback_url: string,
    amount: number
}

export type ArticleResponse = {
    title: string;
    summary: string;
    comments_count: number;
    cover: string;
    date: string;
    link: string;
};

export type TransactionResponse = {
    message: string;
    balance: number;
    amount: number;
    createdAt: string,
    refId: number,
    type: VehicleType | null,
    details?: Details;
    plaque?: Plaque;
    callbackUrl?: string;
};

export type InquireCarArgs = {
    type?: VehicleType,
    details?: Details;
    plaque: Plaque,
    inquiry: boolean
    redirect?: boolean
};

export type InquireCarResponse = {
    message: string
    trace_number: string,
    showDialogInquiry: boolean
}


export type VerificationResponse = {
    message: string
    access_token: string
    token_type: string
    balance: number
    expire_at: number
}

export type GetUserResponse = {
    fullname: string,
    phone: string,
    balance: number
}

export interface ActionResult<T = undefined> {
    message?: string
    success: boolean
    status?: number
    data?: T,
    callbackUrl?: string
}

export interface ServicesStatus {
    services: {
        [key: string]: "ACTIVE" | "DEACTIVE";
    }
}
export interface DrivingLicensePointType {
    national_id: string,
    license_number: string,
    mobile_number: string
}


// *************************** in contexts ****************************
export interface UserPayload {
    fullName: string
    phone: string
    balance: number
}
