'use server'

import {
    ActionResult,
    CarPaymentDetails,
    DrivingLicensePointType,
    HistoryTransaction,
    InquireCarArgs,
    InquireCarResponse,
} from "@/lib/types/action-types";
import {handleActionErrorResponse} from "@/utils/helpers";
import inquiryService from "@/lib/services/inquiry.service";
import {apiClient} from "@/lib/apiClient";
import {cookies} from "next/headers";

export async function postInquireVehicle(args: InquireCarArgs): Promise<ActionResult<InquireCarResponse>> {
    try {
        const responseResult = await inquiryService.inquireVehicle<InquireCarResponse>({payload: args});
        console.log("++++++++++++++++++", responseResult);
        return {
            success: true,
            data: responseResult.data,
        }

    } catch (error: any) {
        console.log("-----------------------", error);
        return handleActionErrorResponse(error)
    }
}

export async function getVehicleInquiry(traceNumber: string): Promise<ActionResult<CarPaymentDetails>> {
    try {
        const responseResult = await inquiryService.getInquiry<CarPaymentDetails>(traceNumber)
        return {
            success: true,
            data: responseResult.data,
        }
    } catch (error: any) {
        return handleActionErrorResponse(error)
    }
}

export async function getInquiryHistory(): Promise<ActionResult<HistoryTransaction[]>> {
    try {
        const responseResult = await inquiryService.getInquiryHistory<HistoryTransaction[]>()
        return {
            success: true,
            data: responseResult.data,
        }
    } catch (error: any) {
        return handleActionErrorResponse(error)
    }
}

export const getViolationImageAction = async (id: string) => {
    try {
        const cookieStore = await cookies();
        const accessToken = cookieStore.get("authorization")?.value;
        const response = await apiClient(`user/inquiry/picture`, {
            method: "POST",
            headers: {Authorization: `${accessToken}`},
            body: {inquiryId: id},
        })

        return await response.json()
    } catch (error: unknown) {
        console.log(error);

    }
}

export const getDrivingLicensePoint = async (data: DrivingLicensePointType) => {
    try {
        const response = await apiClient('user/inquiry/certificate/negative-points', {
            method: "POST",
            body: {
                details: data
            },
        })
        return await response.json()
    } catch (error) {
        console.error("Error getting driving license point:", error);
        return { success: false, error: "Failed to get driving license point" };        
    }
    
}    
export const getDrivingLicensePointByTrace = async (trace: string) => {
    try {
        const response = await apiClient(`user/inquiry/certificate/${trace}/negative-points`, {
            method: "GET",
        })
        return await response.json()
    } catch (error) {
        console.error("Error getting driving license point:", error);
        return { success: false, error: "Failed to get driving license point" };        
    }
    
}    